import { deleteComment } from '@/actions/community';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ConfirmDialog } from '@/components/ui/confirm-dialog';
import { cn, formatRelativeTime } from '@/lib/utils';
import { Prisma } from '@prisma/client';
import { X } from 'lucide-react';
import { useSession } from 'next-auth/react';
import { memo, useState } from 'react';
import { toast } from 'sonner';
import { useSWRConfig } from 'swr';
import { Button } from '../../ui/button';

// Prisma validator를 사용하여 댓글과 사용자 정보를 포함하는 타입을 정의합니다.
const postCommentWithUser = Prisma.validator<Prisma.PostCommentDefaultArgs>()({
  include: { user: { select: { id: true, name: true, image: true } } },
});

type PostCommentWithUser = Prisma.PostCommentGetPayload<
  typeof postCommentWithUser
>;

interface PostCommentItemProps {
  comment: PostCommentWithUser;
  className?: string;
}

/**
 * 댓글 표시 컴포넌트
 *
 * React.memo로 최적화하여 댓글 데이터가 변경되지 않으면 리렌더링하지 않습니다.
 */
function PostCommentDisplay({ comment, className = '' }: PostCommentItemProps) {
  const { data: session } = useSession();
  const { mutate } = useSWRConfig();
  const currentUserId = session?.user?.id;
  const commentAuthorId = comment.user.id;
  const [isDeleting, setIsDeleting] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  const timeAgo = formatRelativeTime(comment.createdAt);

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowConfirmModal(true);
  };

  const handleConfirmDelete = async () => {
    setShowConfirmModal(false);
    if (isDeleting) return;
    setIsDeleting(true);

    try {
      const result = await deleteComment({ id: comment.id });
      if (result.success) {
        toast.success('댓글이 삭제되었습니다.');
        mutate(['comments', comment.postId]);
      } else {
        // 실패 케이스는 catch에서 처리
      }
    } catch (error) {
      console.error('Failed to delete comment:', error);
      toast.error(
        error instanceof Error
          ? error.message
          : '댓글 삭제 중 오류가 발생했습니다.'
      );
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCloseConfirmModal = () => {
    setShowConfirmModal(false);
  };

  return (
    <>
      <div
        className={cn(
          'bg-muted relative flex items-start space-x-3 rounded-lg p-4',
          className
        )}
      >
        {currentUserId && currentUserId === commentAuthorId && (
          <Button
            variant="ghost"
            size="icon"
            className="text-muted-foreground hover:text-destructive absolute top-2 right-1 h-6 w-6"
            onClick={handleDeleteClick}
            disabled={isDeleting}
            aria-label="댓글 삭제"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
        <Avatar className="h-8 w-8">
          <AvatarImage src={comment.user.image ?? undefined} />
          <AvatarFallback>
            {comment.user.name ? comment.user.name[0] : '?'}
          </AvatarFallback>
        </Avatar>
        <div className="flex-1">
          <div className="mb-1 flex items-baseline space-x-2">
            <span className="text-foreground text-sm font-semibold">
              {comment.user.name || '익명'}
            </span>
            <span className="text-muted-foreground text-xs">{timeAgo}</span>
          </div>
          <p className="text-foreground comment-content text-sm">
            {comment.content}
          </p>
        </div>
      </div>

      <ConfirmDialog
        isOpen={showConfirmModal}
        onClose={handleCloseConfirmModal}
        onConfirm={handleConfirmDelete}
        title="댓글 삭제 확인"
        description="정말로 이 댓글을 삭제하시겠습니까?"
        confirmText={isDeleting ? '삭제 중...' : '삭제'}
      />
    </>
  );
}

/**
 * 댓글 표시 컴포넌트를 메모이제이션하여 성능 최적화
 *
 * 댓글 데이터가 변경되지 않으면 리렌더링하지 않습니다.
 */
export default memo(PostCommentDisplay, (prevProps, nextProps) => {
  // 댓글 ID와 내용이 같으면 리렌더링하지 않음
  return (
    prevProps.comment.id === nextProps.comment.id &&
    prevProps.comment.content === nextProps.comment.content &&
    prevProps.comment.updatedAt === nextProps.comment.updatedAt &&
    prevProps.className === nextProps.className
  );
});
