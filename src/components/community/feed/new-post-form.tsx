'use client';

import { getLinkPreview } from '@/actions/link-preview';
import { createPost } from '@/actions/posts';
import { LinkPreview } from '@/components/community/feed/link-preview';
import { TiptapEditor, useEditorState } from '@/components/dynamic';
import { Button, InnerShadowButton } from '@/components/ui/button';
import { ConfirmDialog } from '@/components/ui/confirm-dialog';
import { Label } from '@/components/ui/label';
import { DEFAULT_COUNTRY_CODE, useCountries } from '@/hooks/use-countries'; // 국가 정보 훅 import
import {
  useClearDraft,
  useDraftPost,
  useSaveDraft,
  useTriggerPostListRefresh,
} from '@/store';
import { PreviewData } from '@/types/preview';
import { useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  forwardRef,
  Suspense,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { toast } from 'sonner';

const NewPostForm = forwardRef<{ hasContent: () => boolean }>(
  function PostForm(
    _props, // 사용하지 않는 props는 언더스코어로 표시
    ref
  ) {
    const router = useRouter();
    const searchParams = useSearchParams();
    const { status } = useSession();
    const { contentText, handleChangeText, contentHtml, handleChangeHtml } =
      useEditorState();
    const [tags, setTags] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [showConfirmModal, setShowConfirmModal] = useState(false);
    const modalRef = useRef<HTMLDivElement>(null);
    const [detectedUrl, setDetectedUrl] = useState<string | null>(null);
    const [previewData, setPreviewData] = useState<PreviewData | null>(null);
    // 최적화된 선택자 함수 사용
    const triggerPostListRefresh = useTriggerPostListRefresh();
    const draftPost = useDraftPost();
    const saveDraft = useSaveDraft();
    const clearDraft = useClearDraft();

    // useCountries 훅 사용
    const { countries } = useCountries();

    // 국가 선택 상태 관리
    const [selectedCountry, setSelectedCountry] =
      useState(DEFAULT_COUNTRY_CODE); // 기본값은 한국

    // URL에서 country 파라미터 가져오기
    useEffect(() => {
      const countryParam = searchParams.get('country');
      if (countryParam && countries.some((c) => c.code === countryParam)) {
        setSelectedCountry(countryParam);
      }
    }, [searchParams, countries]);

    // 로그인 상태 확인 - 미들웨어에서 이미 처리하지만 추가 안전장치
    useEffect(() => {
      if (status === 'unauthenticated') {
        toast.error('로그인이 필요합니다.');
        // 이미 미들웨어에서 리디렉션을 처리하지만, 추가 안전장치로 유지
        router.push('/auth/signin?callbackUrl=/');
      }
    }, [status, router]);

    // 임시 저장된 게시물 불러오기
    useEffect(() => {
      if (draftPost && contentText.trim() === '') {
        handleChangeText(draftPost.content);
        if (draftPost.tags.length > 0) {
          setTags(draftPost.tags.join(', '));
        }
      }
    }, [draftPost, contentText, handleChangeText, setTags]);

    // 작성 중인 내용이 있는지 확인
    const hasContent = () => {
      return contentText.trim() !== '' || tags.trim() !== '';
    };

    // ref를 통해 외부에서 hasContent 함수에 접근할 수 있도록 설정
    useImperativeHandle(ref, () => ({
      hasContent,
    }));

    // 취소 버튼 클릭 시 작성 중인 내용이 있으면 임시 저장 후 확인 모달 표시
    const handleCancelClick = () => {
      if (hasContent()) {
        // 임시 저장
        saveDraft({
          content: contentText,
          title: '',
          tags: tags
            .split(',')
            .map((tag) => tag.trim())
            .filter(Boolean),
        });
        setShowConfirmModal(true);
      } else {
        router.back();
      }
    };

    // 확인 모달에서 확인 버튼 클릭 시 이전 화면으로 돌아가기
    const handleConfirmNavigation = () => {
      setShowConfirmModal(false);
      router.back();
    };

    // 확인 모달 닫기
    const handleCloseConfirmModal = () => {
      setShowConfirmModal(false);
    };

    // URL 감지 및 미리보기 데이터 가져오기
    useEffect(() => {
      const urlRegex = /(https?:\/\/[^\s]+)/g;
      const matches = contentText.match(urlRegex);

      if (matches && matches.length > 0) {
        const currentUrl = matches[0];
        setDetectedUrl(currentUrl);

        // 서버 액션을 사용하여 미리보기 데이터 가져오기
        getLinkPreview(currentUrl)
          .then((data) => {
            setPreviewData(data);
          })
          .catch(() => {
            setPreviewData(null);
          });
      } else {
        setDetectedUrl(null);
        setPreviewData(null);
      }
    }, [contentText]);

    const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault();

      // 로그인 상태 확인 - 미들웨어에서 이미 처리하지만 추가 안전장치
      if (status === 'unauthenticated') {
        toast.error('로그인이 필요합니다.');
        return;
      }

      if (!contentText.trim()) {
        toast.error('내용을 입력해주세요.');
        return;
      }

      setIsSubmitting(true);

      try {
        // Convert comma-separated tags to array
        const tagArray = tags
          .split(',')
          .map((tag) => tag.trim())
          .filter((tag) => tag.length > 0);

        const result = await createPost({
          contentText: contentText,
          contentHtml: contentHtml,
          tags: tagArray,
          preview: previewData,
          country: selectedCountry, // 선택한 국가 코드 전달
        });

        if (result.success) {
          // 폼 초기화
          setTags('');
          handleChangeText(''); // 에디터 내용 초기화
          handleChangeHtml(''); // 에디터 HTML 초기화

          // 게시글 목록 갱신 트리거
          triggerPostListRefresh();

          // 임시 저장 내용 지우기
          clearDraft();

          // 성공 메시지 표시
          toast.success('게시글이 작성되었습니다.');

          // 약간의 지연 후 이전 화면으로 돌아가기
          // 지연을 통해 SWR이 데이터를 재검증할 시간을 확보
          setTimeout(() => {
            router.back();
          }, 300);
        } else {
          toast.error(result.message || '게시글 작성에 실패했습니다.');
        }
      } catch (error) {
        console.error('Post creation error:', error);
        toast.error('게시글 작성 중 오류가 발생했습니다.');
      } finally {
        setIsSubmitting(false);
      }
    };

    return (
      <>
        {/* 오버레이 + 중앙정렬 + 더 명확한 모달 */}
        <div
          ref={modalRef}
          className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto bg-black/60"
          onKeyDown={(e: React.KeyboardEvent<HTMLDivElement>) => {
            if (e.key === 'Escape' && !e.nativeEvent.isComposing) {
              handleCancelClick();
            }
          }}
          tabIndex={-1}
        >
          <form
            onSubmit={handleSubmit}
            className="mx-auto w-full max-w-2xl"
          >
            <div
              onClick={(e) => e.stopPropagation()}
              className="max-h-[90vh] w-full space-y-6 overflow-y-auto rounded-2xl border border-zinc-200 bg-white p-4 shadow-2xl dark:border-zinc-700 dark:bg-zinc-900"
            >
              <div className="mb-4 rounded-md border border-amber-200 bg-amber-50 p-3 text-sm text-amber-800 dark:border-amber-800 dark:bg-amber-950/30 dark:text-amber-200">
                <p className="font-medium">게시글 작성 시 주의사항:</p>
                <ul className="mt-1 list-disc pl-5">
                  <li>욕설, 비방, 혐오 표현은 사용하지 말아주세요.</li>
                  <li>타인을 모욕하거나 명예를 훼손하는 내용은 금지됩니다.</li>
                  <li>
                    허위 정보나 사실 확인이 되지 않은 내용은 자제해주세요.
                  </li>
                  <li>개인정보 노출에 주의해주세요.</li>
                </ul>
              </div>

              {/* 국가 선택 Badge */}
              <div>
                <Label className="mb-2 block">게시글 국가 선택</Label>
                <div className="flex flex-wrap gap-2">
                  {countries
                    .filter((c) => c.enabled) // 활성화된 국가만 포함
                    .map((country) => (
                      <InnerShadowButton
                        key={country.code}
                        variant={
                          selectedCountry === country.code
                            ? 'default'
                            : 'outline'
                        }
                        size="sm"
                        className="px-3 py-1 text-sm"
                        onClick={() => setSelectedCountry(country.code)}
                      >
                        {country.name} ({country.displayName})
                      </InnerShadowButton>
                    ))}
                </div>
              </div>

              <Suspense
                fallback={
                  <div className="post-content flex min-h-[200px] min-w-[400px] items-center justify-center">
                    에디터 로딩 중...
                  </div>
                }
              >
                <TiptapEditor
                  content={contentHtml}
                  onChangeText={handleChangeText}
                  onChangeHtml={handleChangeHtml}
                  placeholder="무엇이든 자유롭게"
                  className="post-content min-h-[200px] min-w-[400px]"
                />
              </Suspense>
              {detectedUrl && (
                <div className="mt-4">
                  <LinkPreview url={detectedUrl} />
                </div>
              )}
              {/* 태그 입력란 등 추가 요소가 있다면 여기에 */}
              <div className="flex justify-end gap-3 pt-2">
                <Button
                  variant="outline"
                  onClick={handleCancelClick}
                  disabled={isSubmitting}
                  type="button"
                >
                  취소
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? '게시 중...' : '게시하기'}
                </Button>
              </div>
            </div>
          </form>
        </div>

        {/* 확인 모달 */}
        <ConfirmDialog
          isOpen={showConfirmModal}
          onClose={handleCloseConfirmModal}
          onConfirm={handleConfirmNavigation}
          title="작성 취소"
          description="작성 중인 내용이 삭제됩니다. 정말 취소하시겠습니까?"
          cancelText="계속 작성하기"
          confirmText="취소하기"
        />
      </>
    );
  }
);

export default NewPostForm;
