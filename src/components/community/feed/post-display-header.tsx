'use client';

import { deletePost } from '@/actions/posts';
import { ConfirmDialog } from '@/components/ui/confirm-dialog';
import { useSetSelectedPostId, useTriggerPostListRefresh } from '@/store';
import { X } from 'lucide-react';
import React, { useState } from 'react';
import { toast } from 'sonner';
import { Button } from '../../ui/button';

interface PostDisplayHeaderProps {
  postId: string;
  postAuthorId?: string | null;
  currentUserId?: string | null;
}

export function PostDisplayHeader({
  postId,
  postAuthorId,
  currentUserId,
}: PostDisplayHeaderProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const triggerPostListRefresh = useTriggerPostListRefresh();
  const setSelectedPostId = useSetSelectedPostId();

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setShowConfirmModal(true);
  };

  // 게시물 선택 처리
  const handlePostSelect = (e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedPostId(postId);
  };

  const handleConfirmDelete = async () => {
    setShowConfirmModal(false);
    if (isDeleting) return;
    setIsDeleting(true);

    try {
      const result = await deletePost({ id: postId });
      if (result.success) {
        toast.success('게시글이 삭제되었습니다.');
        triggerPostListRefresh();
      } else {
        // 에러는 catch 블록에서 처리
      }
    } catch (error) {
      console.error('Failed to delete post:', error);
      toast.error(
        error instanceof Error
          ? error.message
          : '게시글 삭제 중 오류가 발생했습니다.'
      );
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCloseConfirmModal = () => {
    setShowConfirmModal(false);
  };

  // 삭제 버튼 렌더링 조건
  const showDeleteButton = currentUserId && currentUserId === postAuthorId;

  if (!showDeleteButton) {
    return null;
  }

  return (
    <div className="flex justify-end">
      <Button
        variant="ghost"
        size="icon"
        className="text-muted-foreground hover:text-destructive h-6 w-6 hover:cursor-pointer"
        onClick={handleDeleteClick}
        disabled={isDeleting}
        aria-label="게시글 삭제"
      >
        <X className="h-4 w-4" />
      </Button>

      {/* 삭제 확인 모달 */}
      <ConfirmDialog
        isOpen={showConfirmModal}
        onClose={handleCloseConfirmModal}
        onConfirm={handleConfirmDelete}
        title="게시글 삭제 확인"
        description="정말로 이 게시글을 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다."
        confirmText={isDeleting ? '삭제 중...' : '삭제'}
      />
    </div>
  );
}
