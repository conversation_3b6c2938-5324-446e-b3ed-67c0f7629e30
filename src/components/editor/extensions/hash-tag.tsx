import { Extension } from '@tiptap/react';
import { Node as ProseMirrorNode } from 'prosemirror-model';
import { EditorState, Plugin, PluginKey } from 'prosemirror-state';
import { Decoration, DecorationSet } from 'prosemirror-view';

const hashtagPluginKey = new PluginKey('hashtagDecoration');

export const Hashtag = Extension.create({
  name: 'hashtag',

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: hashtagPluginKey,
        props: {
          decorations: (state: EditorState) => {
            const decorations: Decoration[] = [];
            const hashtagRegex = /#[\w가-힣]+/g;

            state.doc.descendants((node: ProseMirrorNode, pos: number) => {
              if (node.isText && node.text) {
                let match;
                while ((match = hashtagRegex.exec(node.text)) !== null) {
                  const from = pos + match.index;
                  const to = from + match[0].length;
                  decorations.push(
                    Decoration.inline(from, to, {
                      class:
                        'font-black bg-accent text-accent-foreground px-1 py-0.5 rounded-md',
                    })
                  );
                }
              }
            });

            return DecorationSet.create(state.doc, decorations);
          },
        },
      }),
    ];
  },
});
