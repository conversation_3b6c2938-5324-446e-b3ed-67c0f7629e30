/**
 * 동적으로 로드되는 컴포넌트 모음
 *
 * 이 파일은 코드 스플리팅을 위해 동적으로 로드되는 컴포넌트들을 내보냅니다.
 */

'use client';

import { dynamicCommunity, dynamicEditor, dynamicModal } from '@/lib/dynamic';

// 모달 컴포넌트
export const ConfirmDialog = dynamicModal('confirm-dialog');

// 에디터 컴포넌트
export const TiptapEditor = dynamicEditor('tiptap-editor');
export const EditorMenuBar = dynamicEditor('editor-menu-bar');

// 커뮤니티 컴포넌트
export const PostDisplay = dynamicCommunity('feed/post-display.optimized');
export const PostCommentList = dynamicCommunity(
  'feed/post-comment-list.optimized'
);

// 에디터 훅 내보내기
export { useEditorState } from '@/components/editor/dynamic/tiptap-editor';
