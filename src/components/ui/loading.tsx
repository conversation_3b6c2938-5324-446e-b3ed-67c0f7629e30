'use client';

import { cn } from '@/lib/utils';

interface LoadingProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'primary' | 'secondary';
}

/**
 * 로딩 스피너 컴포넌트
 *
 * @param className 추가 클래스 이름
 * @param size 크기 (sm, md, lg)
 * @param variant 변형 (default, primary, secondary)
 */
export function Loading({
  className,
  size = 'md',
  variant = 'primary',
}: LoadingProps) {
  const sizeClasses = {
    sm: 'h-4 w-4 border-2',
    md: 'h-6 w-6 border-2',
    lg: 'h-8 w-8 border-3',
  };

  const variantClasses = {
    default:
      'border-gray-300 border-t-gray-800 dark:border-gray-700 dark:border-t-gray-300',
    primary: 'border-gray-300 border-t-primary',
    secondary: 'border-gray-300 border-t-secondary',
  };

  return (
    <div
      className={cn(
        'animate-spin rounded-full',
        sizeClasses[size],
        variantClasses[variant],
        className
      )}
    />
  );
}

/**
 * 전체 화면 로딩 컴포넌트
 *
 * @param className 추가 클래스 이름
 */
export function FullScreenLoading({ className }: { className?: string }) {
  return (
    <div
      className={cn(
        'bg-background/80 fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm',
        className
      )}
    >
      <Loading size="lg" />
    </div>
  );
}

/**
 * 모달 로딩 컴포넌트
 *
 * @param className 추가 클래스 이름
 */
export function ModalLoading({ className }: { className?: string }) {
  return (
    <div
      className={cn(
        'bg-foreground/20 fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm',
        className
      )}
    >
      <div className="bg-background rounded-lg p-6 shadow-lg">
        <div className="flex flex-col items-center gap-2">
          <Loading size="lg" />
          <p className="text-muted-foreground text-sm">로딩 중...</p>
        </div>
      </div>
    </div>
  );
}
